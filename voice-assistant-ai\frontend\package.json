{"name": "voice-assistant-ai-frontend", "version": "1.0.0", "description": "Voice Assistant AI Frontend - React application with voice interface", "private": true, "dependencies": {"@aws-amplify/ui-react": "^5.3.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^27.5.2", "@types/node": "^16.18.38", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "aws-amplify": "^5.3.8", "axios": "^1.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.1", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "styled-components": "^6.0.5", "@types/styled-components": "^5.1.26", "react-speech-recognition": "^3.10.0", "regenerator-runtime": "^0.13.11", "uuid": "^9.0.0", "@types/uuid": "^9.0.2", "date-fns": "^2.30.0", "react-hot-toast": "^2.4.1", "framer-motion": "^10.12.18", "react-icons": "^4.10.1", "recharts": "^2.7.2", "react-helmet-async": "^1.3.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write src/**/*.{ts,tsx,js,jsx,json,css,md}", "type-check": "tsc --noEmit", "analyze": "npm run build && npx bundle-analyzer build/static/js/*.js", "deploy": "npm run build && aws s3 sync build/ s3://$REACT_APP_S3_BUCKET --delete", "deploy:amplify": "amplify publish"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "rules": {"@typescript-eslint/no-unused-vars": "warn", "react-hooks/exhaustive-deps": "warn"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-speech-recognition": "^3.9.0", "eslint": "^8.44.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.0", "bundle-analyzer": "^0.1.0"}, "proxy": "http://localhost:8000"}