# Spotify Clone Terraform Infrastructure

This Terraform configuration creates the AWS infrastructure needed for the Spotify Clone application.

## Architecture

The infrastructure includes:

- **EC2 Instance**: Ubuntu server with Docker, Node.js, and application dependencies
- **Security Group**: Configured for web traffic and SSH access
- **Elastic IP**: Static IP address for the application
- **S3 Bucket**: Storage for music files and assets
- **IAM Role & Policies**: Permissions for EC2 to access S3 and CloudWatch
- **CloudWatch**: Monitoring and logging

## Prerequisites

1. **AWS CLI configured** with appropriate credentials
2. **Terraform installed** (version >= 1.0)
3. **AWS Key Pair** created in your target region

## Quick Start

1. **Clone and navigate to terraform directory**:
   ```bash
   cd terraform
   ```

2. **Create your variables file**:
   ```bash
   cp terraform.tfvars.example terraform.tfvars
   ```

3. **Edit terraform.tfvars** with your specific values:
   ```hcl
   aws_region    = "us-east-1"
   key_pair_name = "your-key-pair-name"
   # ... other variables
   ```

4. **Initialize Terraform**:
   ```bash
   terraform init
   ```

5. **Plan the deployment**:
   ```bash
   terraform plan
   ```

6. **Apply the configuration**:
   ```bash
   terraform apply
   ```

7. **Get the outputs**:
   ```bash
   terraform output
   ```

## Configuration Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `aws_region` | AWS region for resources | `us-east-1` | No |
| `project_name` | Name of the project | `spotify-clone` | No |
| `environment` | Environment name | `dev` | No |
| `instance_type` | EC2 instance type | `t3.medium` | No |
| `key_pair_name` | AWS key pair name | `spotify-clone-key` | Yes |
| `allowed_cidr_blocks` | CIDR blocks for SSH access | `["0.0.0.0/0"]` | No |
| `s3_bucket_name` | S3 bucket name prefix | `spotify-clone-files` | No |

## Outputs

After successful deployment, you'll get:

- `instance_public_ip`: Public IP of the EC2 instance
- `application_url`: URL to access the application
- `ssh_connection`: SSH command to connect to the instance
- `s3_bucket_name`: Name of the created S3 bucket

## Security Considerations

### For Production:

1. **Restrict SSH access**: Update `allowed_cidr_blocks` to your specific IP
2. **Use HTTPS**: Configure SSL/TLS certificates
3. **Enable VPC**: Deploy in a private subnet with NAT gateway
4. **Database**: Use RDS instead of local database
5. **Secrets**: Use AWS Secrets Manager for sensitive data

### Current Security Features:

- Encrypted EBS volumes
- IAM roles with minimal required permissions
- S3 bucket encryption
- Security groups with specific port access

## Monitoring

The infrastructure includes:

- **CloudWatch Logs**: Application and system logs
- **CloudWatch Metrics**: CPU, memory, and disk usage
- **Health Checks**: Basic application health monitoring

## Cleanup

To destroy the infrastructure:

```bash
terraform destroy
```

**Warning**: This will permanently delete all resources including the S3 bucket and any stored files.

## Troubleshooting

### Common Issues:

1. **Key pair not found**: Ensure the key pair exists in your AWS region
2. **Permission denied**: Check AWS credentials and IAM permissions
3. **Instance not accessible**: Verify security group rules and key pair

### Logs:

- User data logs: `/var/log/cloud-init-output.log`
- Application logs: `/home/<USER>/spotify-clone/logs/`
- System logs: Available in CloudWatch

## File Structure

```
terraform/
├── main.tf              # Provider and data sources
├── variables.tf         # Variable definitions
├── ec2.tf              # EC2 instance and security group
├── s3.tf               # S3 bucket configuration
├── iam.tf              # IAM roles and policies
├── outputs.tf          # Output values
├── user_data.sh        # EC2 initialization script
├── terraform.tfvars.example  # Example variables file
└── README.md           # This file
```

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review AWS CloudWatch logs
3. Verify Terraform state and configuration
