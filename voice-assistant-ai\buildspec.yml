version: 0.2

# CodeBuild specification for Voice Assistant AI (Non-Terraform)
# This buildspec handles application deployment without infrastructure changes

env:
  variables:
    PYTHON_VERSION: "3.9"
    NODE_VERSION: "16"
    PROJECT_NAME: "voice-assistant-ai"
  parameter-store:
    AWS_ACCOUNT_ID: "/voice-assistant/aws-account-id"
    S3_BUCKET: "/voice-assistant/s3-bucket"
    LEX_BOT_ID: "/voice-assistant/lex-bot-id"
  secrets-manager:
    DATABASE_URL: "voice-assistant/database:url"
    API_KEYS: "voice-assistant/api-keys:all"

phases:
  install:
    runtime-versions:
      python: $PYTHON_VERSION
      nodejs: $NODE_VERSION
    commands:
      - echo "Installing system dependencies..."
      - apt-get update -y
      - apt-get install -y zip unzip jq
      
      - echo "Installing Python dependencies..."
      - pip install --upgrade pip
      - pip install -r requirements.txt
      
      - echo "Installing Node.js dependencies..."
      - cd frontend && npm ci
      
      - echo "Installing AWS CLI..."
      - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
      - unzip awscliv2.zip
      - ./aws/install

  pre_build:
    commands:
      - echo "Pre-build phase started on `date`"
      - echo "Running security scans..."
      - bandit -r backend/ -f json -o bandit-report.json || true
      
      - echo "Running code quality checks..."
      - black backend/ --check
      - flake8 backend/
      - isort backend/ --check-only
      
      - echo "Running tests..."
      - pytest backend/tests/ -v --cov=backend --cov-report=xml --cov-report=term
      
      - echo "Validating configurations..."
      - python -m json.tool pipeline/codepipeline.json > /dev/null
      
      - echo "Setting up environment variables..."
      - export ENVIRONMENT=${ENVIRONMENT:-dev}
      - export AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION:-us-east-1}

  build:
    commands:
      - echo "Build phase started on `date`"
      
      - echo "Building Lambda functions..."
      - |
        for function_dir in backend/lambda_functions/*/; do
          function_name=$(basename "$function_dir")
          echo "Building $function_name..."
          
          cd "$function_dir"
          
          # Create package directory
          mkdir -p package
          
          # Install dependencies
          if [ -f requirements.txt ]; then
            pip install -r requirements.txt -t package/
          fi
          
          # Copy source code
          cp *.py package/
          
          # Copy shared utilities
          cp -r ../../shared package/ 2>/dev/null || true
          
          # Create deployment package
          cd package
          zip -r "../${function_name}.zip" .
          cd ..
          
          # Clean up
          rm -rf package
          
          cd - > /dev/null
        done
      
      - echo "Building frontend..."
      - cd frontend
      - npm run build
      - cd ..
      
      - echo "Preparing Lex bot configuration..."
      - python pipeline/scripts/prepare_lex_config.py
      
      - echo "Creating deployment artifacts..."
      - mkdir -p artifacts
      - cp backend/lambda_functions/*/*.zip artifacts/
      - cp -r frontend/build artifacts/frontend
      - cp pipeline/scripts/*.py artifacts/

  post_build:
    commands:
      - echo "Post-build phase started on `date`"
      
      - echo "Deploying Lambda functions..."
      - |
        for zip_file in artifacts/*.zip; do
          function_name=$(basename "$zip_file" .zip)
          full_function_name="${PROJECT_NAME}-${function_name}-${ENVIRONMENT}"
          
          echo "Deploying $full_function_name..."
          
          # Update function code
          aws lambda update-function-code \
            --function-name "$full_function_name" \
            --zip-file "fileb://$zip_file" \
            --region $AWS_DEFAULT_REGION
          
          # Wait for update to complete
          aws lambda wait function-updated \
            --function-name "$full_function_name" \
            --region $AWS_DEFAULT_REGION
          
          echo "Successfully deployed $full_function_name"
        done
      
      - echo "Deploying Lex bot..."
      - python artifacts/deploy_lex.py --environment $ENVIRONMENT
      
      - echo "Uploading frontend to S3..."
      - aws s3 sync artifacts/frontend/ s3://${S3_BUCKET}/frontend/ --delete
      
      - echo "Invalidating CloudFront cache..."
      - |
        DISTRIBUTION_ID=$(aws cloudfront list-distributions \
          --query "DistributionList.Items[?Comment=='${PROJECT_NAME}-${ENVIRONMENT}'].Id" \
          --output text)
        
        if [ ! -z "$DISTRIBUTION_ID" ]; then
          aws cloudfront create-invalidation \
            --distribution-id $DISTRIBUTION_ID \
            --paths "/*"
        fi
      
      - echo "Running post-deployment tests..."
      - python pipeline/scripts/health_check.py --environment $ENVIRONMENT
      
      - echo "Generating deployment report..."
      - |
        cat > deployment-report.json << EOF
        {
          "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
          "environment": "$ENVIRONMENT",
          "commit": "$CODEBUILD_RESOLVED_SOURCE_VERSION",
          "build_id": "$CODEBUILD_BUILD_ID",
          "lambda_functions": [
            $(ls artifacts/*.zip | sed 's/artifacts\///g' | sed 's/\.zip//g' | sed 's/^/    "/' | sed 's/$/",/' | sed '$ s/,$//')
          ],
          "status": "success"
        }
        EOF
      
      - echo "Build completed successfully on `date`"

artifacts:
  files:
    - '**/*'
  secondary-artifacts:
    lambda-artifacts:
      files:
        - 'artifacts/*.zip'
      name: lambda-functions
    frontend-artifacts:
      files:
        - 'artifacts/frontend/**/*'
      name: frontend-build
    reports:
      files:
        - 'bandit-report.json'
        - 'coverage.xml'
        - 'deployment-report.json'
      name: build-reports

reports:
  coverage-report:
    files:
      - 'coverage.xml'
    file-format: 'COBERTURAXML'
  security-report:
    files:
      - 'bandit-report.json'
    file-format: 'JSON'

cache:
  paths:
    - '/root/.cache/pip/**/*'
    - 'frontend/node_modules/**/*'
    - '/root/.npm/**/*'
