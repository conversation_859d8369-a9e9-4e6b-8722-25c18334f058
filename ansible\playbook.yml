---
- name: Setup Spotify Clone Server
  hosts: spotify_servers
  become: yes
  vars:
    project_name: spotify-clone
    app_user: ubuntu
    app_dir: /home/<USER>/spotify-clone

  roles:
    - docker
    - env

  tasks:
    - name: Clone repository
      git:
        repo: https://github.com/your-username/spotify-clone.git
        dest: "{{ app_dir }}"
        force: yes
      become_user: "{{ app_user }}"

    - name: Set ownership of application directory
      file:
        path: "{{ app_dir }}"
        owner: "{{ app_user }}"
        group: "{{ app_user }}"
        recurse: yes

    - name: Start application with Docker Compose
      docker_compose:
        project_src: "{{ app_dir }}"
        state: present
      become_user: "{{ app_user }}"
