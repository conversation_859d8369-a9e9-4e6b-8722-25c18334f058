#!/bin/bash

set -e

SERVICE=$1

if [ -z "$SERVICE" ]; then
    echo "Usage: $0 <service>"
    echo "Available services: backend, frontend"
    exit 1
fi

echo "Building $SERVICE..."

case $SERVICE in
    "backend")
        cd backend
        echo "Installing backend dependencies..."
        npm ci
        echo "Building backend Docker image..."
        docker build -t spotify-clone-backend:${BUILD_NUMBER:-latest} .
        ;;
    "frontend")
        cd frontend
        echo "Installing frontend dependencies..."
        npm ci
        echo "Building frontend..."
        npm run build
        echo "Building frontend Docker image..."
        docker build -t spotify-clone-frontend:${BUILD_NUMBER:-latest} .
        ;;
    *)
        echo "Unknown service: $SERVICE"
        exit 1
        ;;
esac

echo "$SERVICE build completed successfully!"
