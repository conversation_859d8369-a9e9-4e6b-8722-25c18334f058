# 🎤 Voice Assistant AI - Complete AWS Setup Guide

A production-ready, serverless voice assistant application built on AWS with Alexa integration, featuring real-time voice processing, natural language understanding, and a modern web interface.

![Voice Assistant AI](https://img.shields.io/badge/AWS-Voice%20Assistant-orange?style=for-the-badge&logo=amazon-aws)
![Serverless](https://img.shields.io/badge/Serverless-Framework-red?style=for-the-badge)
![React](https://img.shields.io/badge/React-18-blue?style=for-the-badge&logo=react)
![Python](https://img.shields.io/badge/Python-3.9-green?style=for-the-badge&logo=python)

## 🎯 What You'll Build

By following this guide, you'll deploy a complete voice assistant system with:

- **🎙️ Voice Interface**: Real-time speech recognition and synthesis
- **🤖 AI Chatbot**: Natural language understanding with Amazon Lex
- **🔊 Alexa Integration**: Native Alexa Skills Kit support
- **🌐 Web Dashboard**: Modern React-based interface
- **🔐 User Authentication**: Secure login with Amazon Cognito
- **📊 Monitoring**: Real-time dashboards and alerts
- **🚀 Auto-scaling**: Serverless architecture that scales automatically

## 🏗️ System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[React Web App]
        B[Alexa Skills Kit]
    end

    subgraph "API Layer"
        C[API Gateway]
    end

    subgraph "Compute Layer"
        D[Chatbot Lambda]
        E[Auth Lambda]
        F[Monitoring Lambda]
    end

    subgraph "AI/ML Layer"
        G[Amazon Lex Bot]
    end

    subgraph "Data Layer"
        H[DynamoDB]
        I[S3 Storage]
    end

    subgraph "Security Layer"
        J[Cognito User Pool]
        K[KMS Encryption]
    end

    A --> C
    B --> C
    C --> D
    C --> E
    C --> F
    D --> G
    D --> H
    D --> I
    E --> J
    F --> H
    K --> H
    K --> I
```

## � Prerequisites & Requirements

### 🛠️ Required Tools

| Tool | Version | Purpose | Download Link |
|------|---------|---------|---------------|
| **AWS CLI** | v2.0+ | AWS service management | [Download](https://aws.amazon.com/cli/) |
| **Terraform** | v1.0+ | Infrastructure as Code | [Download](https://www.terraform.io/downloads) |
| **Node.js** | v18+ | Frontend development | [Download](https://nodejs.org/) |
| **Python** | v3.9+ | Backend development | [Download](https://www.python.org/) |
| **Git** | Latest | Version control | [Download](https://git-scm.com/) |

### 🏦 AWS Account Setup

#### Step 1: Create AWS Account
1. Go to [AWS Console](https://aws.amazon.com/console/)
2. Click "Create a new AWS account"
3. Follow the registration process
4. **Important**: Enable billing alerts in your account

#### Step 2: Create IAM User for Deployment
```bash
# Create IAM user with programmatic access
aws iam create-user --user-name voice-assistant-deployer

# Attach necessary policies
aws iam attach-user-policy --user-name voice-assistant-deployer --policy-arn arn:aws:iam::aws:policy/PowerUserAccess
aws iam attach-user-policy --user-name voice-assistant-deployer --policy-arn arn:aws:iam::aws:policy/IAMFullAccess

# Create access keys
aws iam create-access-key --user-name voice-assistant-deployer
```

#### Step 3: Configure AWS CLI
```bash
aws configure
# AWS Access Key ID: [Enter your access key]
# AWS Secret Access Key: [Enter your secret key]
# Default region name: us-east-1
# Default output format: json
```

### 💰 Cost Estimation

| Service | Monthly Cost (Estimate) | Usage Assumption |
|---------|------------------------|------------------|
| **Lambda** | $5-20 | 100K requests/month |
| **DynamoDB** | $2-10 | 1GB storage, 100K reads/writes |
| **API Gateway** | $3-15 | 100K API calls |
| **S3** | $1-5 | 10GB storage |
| **Cognito** | $0-5 | <50K MAU (free tier) |
| **Lex** | $4-20 | 10K text requests |
| **CloudWatch** | $2-8 | Standard monitoring |
| **Total** | **$17-83/month** | Small to medium usage |

## � Complete Setup Guide

### Phase 1: Project Setup and Configuration

#### Step 1: Clone and Initialize Project
```bash
# Clone the repository
git clone https://github.com/your-org/voice-assistant-ai.git
cd voice-assistant-ai

# Make scripts executable
chmod +x scripts/*.sh
chmod +x pipeline/scripts/*.py

# Install Python dependencies for deployment
pip install boto3 terraform-compliance bandit safety
```

#### Step 2: Environment Configuration
```bash
# Copy environment template
cp infra/terraform/terraform.tfvars.example infra/terraform/terraform.tfvars

# Edit the configuration file
nano infra/terraform/terraform.tfvars
```

**Configure your `terraform.tfvars`:**
```hcl
# Basic Configuration
aws_region = "us-east-1"
project_name = "voice-assistant-ai"
environment = "prod"

# Contact Information
alert_email_addresses = ["<EMAIL>"]

# Feature Flags
enable_alexa_integration = true
enable_web_interface = true
enable_voice_recording = true
enable_analytics = true

# Resource Configuration
lambda_timeout = 30
lambda_memory_size = 512
lambda_reserved_concurrency = 10
dynamodb_billing_mode = "PAY_PER_REQUEST"
log_retention_days = 14
```

### Phase 2: AWS Infrastructure Deployment

#### Step 3: Deploy Core Infrastructure
```bash
# Initialize Terraform
cd infra/terraform
terraform init

# Validate configuration
terraform validate

# Plan deployment (review what will be created)
terraform plan -var-file="terraform.tfvars"

# Deploy infrastructure (this takes 10-15 minutes)
terraform apply -var-file="terraform.tfvars"
```

**What gets created:**
- ✅ API Gateway with REST endpoints
- ✅ Lambda functions (chatbot, auth, monitoring)
- ✅ DynamoDB tables for conversations and sessions
- ✅ S3 buckets for file storage
- ✅ Cognito User Pool for authentication
- ✅ CloudWatch dashboards and alarms
- ✅ KMS keys for encryption
- ✅ IAM roles and policies

#### Step 4: Verify Infrastructure Deployment
```bash
# Check created resources
terraform output

# Verify Lambda functions
aws lambda list-functions --query 'Functions[?starts_with(FunctionName, `voice-assistant-ai`)].[FunctionName,Runtime,LastModified]' --output table

# Verify DynamoDB tables
aws dynamodb list-tables --query 'TableNames[?starts_with(@, `voice-assistant-ai`)]' --output table

# Verify S3 buckets
aws s3 ls | grep voice-assistant-ai
```

### Phase 3: Amazon Lex Bot Configuration

#### Step 5: Create and Configure Lex Bot

**Option A: Using AWS Console (Recommended for beginners)**

1. **Go to Amazon Lex Console**
   - Navigate to [Amazon Lex Console](https://console.aws.amazon.com/lexv2/)
   - Click "Create bot"

2. **Bot Configuration**
   ```
   Bot name: voice-assistant-ai-prod-bot
   Description: Voice Assistant AI Bot for Production
   IAM role: Create a new role
   Language: English (US)
   Voice interaction: Joanna (Neural)
   Session timeout: 5 minutes
   ```

3. **Create Intents**

   **Welcome Intent:**
   ```
   Intent name: WelcomeIntent
   Sample utterances:
   - Hello
   - Hi there
   - Good morning
   - Hey
   - Start

   Response:
   - "Hello! I'm your voice assistant. How can I help you today?"
   ```

   **Help Intent:**
   ```
   Intent name: HelpIntent
   Sample utterances:
   - Help
   - What can you do
   - How do I use this
   - Commands

   Response:
   - "I can help you with various tasks. Try asking me about the weather, news, or setting reminders!"
   ```

4. **Build and Test Bot**
   - Click "Build" (takes 2-3 minutes)
   - Test in the console with sample phrases
   - Create an alias named "prod"

**Option B: Using Automated Script**
```bash
# Deploy Lex bot using automation script
cd pipeline/scripts
python deploy_lex.py \
  --bot-config ../../lex/bot-config.json \
  --locale-config ../../lex/locale-config.json \
  --intents-config ../../lex/intents.json \
  --environment prod \
  --lambda-arn $(aws lambda get-function --function-name voice-assistant-ai-chatbot-prod --query 'Configuration.FunctionArn' --output text)
```

### Phase 4: Lambda Function Deployment

#### Step 6: Package and Deploy Lambda Functions
```bash
# Return to project root
cd ../../

# Package and deploy chatbot function
python pipeline/scripts/package_lambda.py \
  --function-dir backend/lambda_functions/chatbot_handler \
  --function-name chatbot \
  --environment prod \
  --publish-version \
  --update-alias prod

# Package and deploy auth function
python pipeline/scripts/package_lambda.py \
  --function-dir backend/lambda_functions/auth_handler \
  --function-name auth \
  --environment prod \
  --publish-version \
  --update-alias prod

# Package and deploy monitoring function
python pipeline/scripts/package_lambda.py \
  --function-dir backend/lambda_functions/monitoring_handler \
  --function-name monitoring \
  --environment prod \
  --publish-version \
  --update-alias prod
```

#### Step 7: Configure Lambda Environment Variables
```bash
# Get infrastructure outputs
API_GATEWAY_URL=$(cd infra/terraform && terraform output -raw api_gateway_url)
DYNAMODB_TABLE=$(cd infra/terraform && terraform output -raw dynamodb_table_name)
S3_BUCKET=$(cd infra/terraform && terraform output -raw s3_bucket_name)
LEX_BOT_ID=$(aws lexv2-models list-bots --query 'botSummaries[?botName==`voice-assistant-ai-prod-bot`].botId' --output text)

# Update chatbot Lambda environment
aws lambda update-function-configuration \
  --function-name voice-assistant-ai-chatbot-prod \
  --environment Variables="{
    \"DYNAMODB_TABLE_NAME\":\"$DYNAMODB_TABLE\",
    \"S3_BUCKET_NAME\":\"$S3_BUCKET\",
    \"LEX_BOT_ID\":\"$LEX_BOT_ID\",
    \"ENVIRONMENT\":\"prod\"
  }"

# Update auth Lambda environment
aws lambda update-function-configuration \
  --function-name voice-assistant-ai-auth-prod \
  --environment Variables="{
    \"DYNAMODB_TABLE_NAME\":\"$DYNAMODB_TABLE\",
    \"ENVIRONMENT\":\"prod\"
  }"

# Update monitoring Lambda environment
aws lambda update-function-configuration \
  --function-name voice-assistant-ai-monitoring-prod \
  --environment Variables="{
    \"DYNAMODB_TABLE_NAME\":\"$DYNAMODB_TABLE\",
    \"ENVIRONMENT\":\"prod\"
  }"
```

### Phase 5: Frontend Deployment

#### Step 8: Configure and Deploy React Frontend

**Option A: Deploy to AWS Amplify (Recommended)**

1. **Setup Amplify CLI**
```bash
# Install Amplify CLI
npm install -g @aws-amplify/cli

# Configure Amplify
amplify configure
```

2. **Initialize Amplify Project**
```bash
cd frontend

# Initialize Amplify
amplify init
# Project name: voice-assistant-ai-frontend
# Environment: prod
# Default editor: Visual Studio Code
# App type: javascript
# Framework: react
# Source directory: src
# Build directory: build
# Build command: npm run build
# Start command: npm start
```

3. **Configure Environment Variables**
```bash
# Get infrastructure outputs
cd ../infra/terraform
API_GATEWAY_URL=$(terraform output -raw api_gateway_url)
COGNITO_USER_POOL_ID=$(terraform output -raw cognito_user_pool_id)
COGNITO_CLIENT_ID=$(terraform output -raw cognito_user_pool_client_id)
COGNITO_IDENTITY_POOL_ID=$(terraform output -raw cognito_identity_pool_id)
S3_BUCKET=$(terraform output -raw s3_bucket_name)

# Create environment file
cd ../../frontend
cat > .env.production << EOF
REACT_APP_API_GATEWAY_URL=$API_GATEWAY_URL
REACT_APP_COGNITO_USER_POOL_ID=$COGNITO_USER_POOL_ID
REACT_APP_COGNITO_CLIENT_ID=$COGNITO_CLIENT_ID
REACT_APP_COGNITO_IDENTITY_POOL_ID=$COGNITO_IDENTITY_POOL_ID
REACT_APP_S3_BUCKET=$S3_BUCKET
REACT_APP_AWS_REGION=us-east-1
REACT_APP_ENVIRONMENT=prod
EOF
```

4. **Build and Deploy**
```bash
# Install dependencies
npm install

# Build the application
npm run build

# Deploy to Amplify
amplify publish
```

**Option B: Deploy to S3 + CloudFront**
```bash
# Build the application
npm run build

# Deploy to S3
aws s3 sync build/ s3://$(cd ../infra/terraform && terraform output -raw s3_bucket_name) --delete

# Invalidate CloudFront cache (if using CloudFront)
aws cloudfront create-invalidation --distribution-id YOUR_DISTRIBUTION_ID --paths "/*"
```

### Phase 6: Amazon Cognito User Management

#### Step 9: Configure Cognito and Create Test Users

1. **Create Admin User**
```bash
# Get Cognito User Pool ID
USER_POOL_ID=$(cd infra/terraform && terraform output -raw cognito_user_pool_id)

# Create admin user
aws cognito-idp admin-create-user \
  --user-pool-id $USER_POOL_ID \
  --username <EMAIL> \
  --user-attributes Name=email,Value=<EMAIL> \
  --temporary-password TempPassword123! \
  --message-action SUPPRESS

# Set permanent password
aws cognito-idp admin-set-user-password \
  --user-pool-id $USER_POOL_ID \
  --username <EMAIL> \
  --password YourSecurePassword123! \
  --permanent
```

2. **Configure Cognito Settings in AWS Console**
   - Go to [Cognito Console](https://console.aws.amazon.com/cognito/)
   - Select your User Pool
   - Configure:
     - Password policy
     - MFA settings (optional)
     - Email verification
     - Custom attributes

### Phase 7: Monitoring and Alerting Setup

#### Step 10: Configure CloudWatch Dashboards and Alarms

1. **Create CloudWatch Dashboard**
```bash
# Deploy monitoring dashboard
aws cloudwatch put-dashboard \
  --dashboard-name voice-assistant-ai-prod \
  --dashboard-body file://monitoring/dashboards/main-dashboard.json
```

2. **Setup SNS Notifications**
```bash
# Get SNS topic ARN
SNS_TOPIC_ARN=$(cd infra/terraform && terraform output -raw sns_alerts_topic_arn)

# Subscribe to alerts
aws sns subscribe \
  --topic-arn $SNS_TOPIC_ARN \
  --protocol email \
  --notification-endpoint <EMAIL>

# Confirm subscription in your email
```

3. **Deploy CloudWatch Alarms**
```bash
# Deploy Lambda monitoring alarms
cd monitoring/alarms
terraform init
terraform apply -var="sns_topic_arn=$SNS_TOPIC_ARN"
```

### Phase 8: Security Configuration

#### Step 11: Configure Secrets and Security

1. **Store Application Secrets**
```bash
# Create secrets for JWT and encryption
aws secretsmanager create-secret \
  --name voice-assistant-ai/api-keys \
  --description "API keys and secrets for Voice Assistant AI" \
  --secret-string '{
    "jwt_secret":"'$(openssl rand -base64 32)'",
    "encryption_key":"'$(openssl rand -hex 32)'"
  }'

# Create database secrets (if using RDS)
aws secretsmanager create-secret \
  --name voice-assistant-ai/database \
  --description "Database credentials" \
  --secret-string '{
    "username":"admin",
    "password":"'$(openssl rand -base64 16)'"
  }'
```

2. **Configure KMS Key Permissions**
```bash
# Get KMS key ID
KMS_KEY_ID=$(cd infra/terraform && terraform output -raw kms_key_id)

# Update key policy (if needed)
aws kms describe-key --key-id $KMS_KEY_ID
```

### Phase 9: Testing and Validation

#### Step 12: Comprehensive System Testing

1. **API Health Checks**
```bash
# Test API Gateway health endpoint
API_URL=$(cd infra/terraform && terraform output -raw api_gateway_url)
curl -X GET "$API_URL/health"

# Expected response: {"status": "healthy", "timestamp": "..."}
```

2. **Lambda Function Testing**
```bash
# Test chatbot Lambda
aws lambda invoke \
  --function-name voice-assistant-ai-chatbot-prod \
  --payload '{"httpMethod":"POST","body":"{\"message\":\"Hello\",\"type\":\"text\"}"}' \
  response.json

cat response.json

# Test auth Lambda
aws lambda invoke \
  --function-name voice-assistant-ai-auth-prod \
  --payload '{"httpMethod":"GET","path":"/health"}' \
  auth-response.json

cat auth-response.json
```

3. **Frontend Testing**
```bash
# Test frontend deployment
curl -I https://your-amplify-domain.amplifyapp.com
# Should return 200 OK

# Test with browser
echo "Open your browser and navigate to: https://your-amplify-domain.amplifyapp.com"
```

4. **Lex Bot Testing**
```bash
# Test Lex bot directly
aws lexv2-runtime recognize-text \
  --bot-id $LEX_BOT_ID \
  --bot-alias-id TSTALIASID \
  --locale-id en_US \
  --session-id test-session-123 \
  --text "Hello"
```

5. **End-to-End Integration Test**
```bash
# Run integration tests
cd tests
python -m pytest integration/ -v

# Run load tests (optional)
cd load
artillery run load-test.yml
```

## 🎉 Congratulations! Your Voice Assistant is Live!

After completing all steps, you should have:

- ✅ **Working Voice Assistant**: Accessible via web interface
- ✅ **Alexa Integration**: Ready for Alexa Skills Kit
- ✅ **Secure Authentication**: User registration and login
- ✅ **Real-time Monitoring**: CloudWatch dashboards and alerts
- ✅ **Scalable Infrastructure**: Auto-scaling serverless architecture

### 🌐 Access Your Application

1. **Web Interface**: `https://your-amplify-domain.amplifyapp.com`
2. **API Endpoints**: `https://your-api-gateway-url.execute-api.us-east-1.amazonaws.com/prod`
3. **CloudWatch Dashboard**: [AWS CloudWatch Console](https://console.aws.amazon.com/cloudwatch/)

## 🔧 Troubleshooting Guide

### Common Issues and Solutions

#### 1. **Terraform Deployment Fails**
```bash
# Check AWS credentials
aws sts get-caller-identity

# Verify permissions
aws iam get-user

# Check Terraform state
terraform state list

# Force unlock if needed
terraform force-unlock <LOCK_ID>
```

#### 2. **Lambda Function Errors**
```bash
# Check Lambda logs
aws logs tail /aws/lambda/voice-assistant-ai-chatbot-prod --follow

# Check function configuration
aws lambda get-function-configuration --function-name voice-assistant-ai-chatbot-prod

# Update function timeout if needed
aws lambda update-function-configuration \
  --function-name voice-assistant-ai-chatbot-prod \
  --timeout 60
```

#### 3. **Lex Bot Not Responding**
```bash
# Check bot status
aws lexv2-models describe-bot --bot-id $LEX_BOT_ID

# Rebuild bot if needed
aws lexv2-models build-bot-locale \
  --bot-id $LEX_BOT_ID \
  --bot-version DRAFT \
  --locale-id en_US
```

#### 4. **Frontend Not Loading**
```bash
# Check Amplify deployment status
amplify status

# Check environment variables
cat frontend/.env.production

# Rebuild and redeploy
cd frontend
npm run build
amplify publish
```

#### 5. **Authentication Issues**
```bash
# Check Cognito User Pool
aws cognito-idp describe-user-pool --user-pool-id $USER_POOL_ID

# Reset user password
aws cognito-idp admin-set-user-password \
  --user-pool-id $USER_POOL_ID \
  --username <EMAIL> \
  --password NewPassword123! \
  --permanent
```

#### 6. **DynamoDB Throttling**
```bash
# Check table metrics
aws cloudwatch get-metric-statistics \
  --namespace AWS/DynamoDB \
  --metric-name ConsumedReadCapacityUnits \
  --dimensions Name=TableName,Value=voice-assistant-ai-prod-conversations \
  --start-time $(date -u -d '1 hour ago' +%Y-%m-%dT%H:%M:%SZ) \
  --end-time $(date -u +%Y-%m-%dT%H:%M:%SZ) \
  --period 300 \
  --statistics Sum

# Switch to provisioned capacity if needed
aws dynamodb modify-table \
  --table-name voice-assistant-ai-prod-conversations \
  --billing-mode PROVISIONED \
  --provisioned-throughput ReadCapacityUnits=10,WriteCapacityUnits=10
```

### 📊 Monitoring and Maintenance

#### Daily Monitoring Checklist
- [ ] Check CloudWatch dashboard for errors
- [ ] Review Lambda function performance
- [ ] Monitor DynamoDB capacity usage
- [ ] Check API Gateway response times
- [ ] Verify Cognito user activity

#### Weekly Maintenance Tasks
- [ ] Review CloudWatch logs for patterns
- [ ] Update Lambda function dependencies
- [ ] Check security alerts
- [ ] Review cost optimization opportunities
- [ ] Test backup and recovery procedures

#### Monthly Tasks
- [ ] Security audit and updates
- [ ] Performance optimization review
- [ ] Cost analysis and optimization
- [ ] Update documentation
- [ ] Review and update monitoring thresholds

## 🚀 Advanced Configuration

### Alexa Skills Kit Integration

1. **Create Alexa Skill**
   - Go to [Alexa Developer Console](https://developer.amazon.com/alexa/console/ask)
   - Create new skill: "Voice Assistant AI"
   - Choose "Custom" model

2. **Configure Skill Endpoint**
   ```
   Endpoint Type: HTTPS
   Default Region: https://your-api-gateway-url.execute-api.us-east-1.amazonaws.com/prod/alexa
   SSL Certificate: Wildcard certificate for a sub-domain
   ```

3. **Add Intents and Utterances**
   ```json
   {
     "intents": [
       {
         "name": "HelloIntent",
         "samples": ["hello", "hi", "hey there"]
       },
       {
         "name": "HelpIntent",
         "samples": ["help", "what can you do", "how do I use this"]
       }
     ]
   }
   ```

### Multi-Environment Setup

#### Development Environment
```bash
# Deploy dev environment
terraform apply -var="environment=dev" -var-file="terraform.tfvars"

# Deploy dev Lambda functions
python pipeline/scripts/package_lambda.py --environment dev
```

#### Staging Environment
```bash
# Deploy staging environment
terraform apply -var="environment=staging" -var-file="terraform.tfvars"

# Deploy staging Lambda functions
python pipeline/scripts/package_lambda.py --environment staging
```

### Performance Optimization

#### Lambda Optimization
```bash
# Enable provisioned concurrency for better performance
aws lambda put-provisioned-concurrency-config \
  --function-name voice-assistant-ai-chatbot-prod \
  --qualifier prod \
  --provisioned-concurrency-units 5
```

#### DynamoDB Optimization
```bash
# Enable auto-scaling
aws application-autoscaling register-scalable-target \
  --service-namespace dynamodb \
  --resource-id table/voice-assistant-ai-prod-conversations \
  --scalable-dimension dynamodb:table:ReadCapacityUnits \
  --min-capacity 5 \
  --max-capacity 100
```

## 📚 Additional Resources

### Documentation
- 📖 [Architecture Guide](docs/ARCHITECTURE.md) - Detailed system architecture
- 🚀 [Deployment Guide](docs/DEPLOYMENT_GUIDE.md) - Advanced deployment options
- 🔒 [Security Guide](docs/SECURITY.md) - Security best practices
- 📊 [Monitoring Guide](docs/MONITORING.md) - Monitoring and alerting setup
- 🔧 [API Documentation](docs/API.md) - Complete API reference

### AWS Service Documentation
- [Amazon Lex Developer Guide](https://docs.aws.amazon.com/lex/)
- [AWS Lambda Developer Guide](https://docs.aws.amazon.com/lambda/)
- [Amazon DynamoDB Developer Guide](https://docs.aws.amazon.com/dynamodb/)
- [Amazon Cognito Developer Guide](https://docs.aws.amazon.com/cognito/)
- [Amazon API Gateway Developer Guide](https://docs.aws.amazon.com/apigateway/)

### Community and Support
- 💬 [GitHub Issues](https://github.com/your-org/voice-assistant-ai/issues) - Bug reports and feature requests
- 📧 [Email Support](mailto:<EMAIL>) - Direct support
- 📖 [Wiki](https://github.com/your-org/voice-assistant-ai/wiki) - Community documentation
- 🎥 [Video Tutorials](https://youtube.com/playlist/voice-assistant-ai) - Step-by-step videos

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup
```bash
# Clone for development
git clone https://github.com/your-org/voice-assistant-ai.git
cd voice-assistant-ai

# Install development dependencies
pip install -r requirements-dev.txt
npm install --prefix frontend

# Run tests
make test

# Run linting
make lint
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- AWS for providing excellent cloud services
- The open-source community for amazing tools and libraries
- Contributors who helped build and improve this project

---

## 📞 Need Help?

If you encounter any issues:

1. **Check the troubleshooting section** above
2. **Review the logs** in CloudWatch
3. **Search existing issues** on GitHub
4. **Create a new issue** with detailed information
5. **Contact support** for urgent issues

**Happy building! 🚀**
- Python >= 3.9
- Docker (for local development)

### Deployment

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd voice-assistant-ai
   ```

2. **Install dependencies**:
   ```bash
   make install
   ```

3. **Configure environment**:
   ```bash
   cp infra/terraform/terraform.tfvars.example infra/terraform/terraform.tfvars
   # Edit terraform.tfvars with your configuration
   ```

4. **Deploy infrastructure**:
   ```bash
   make deploy-infra
   ```

5. **Deploy application**:
   ```bash
   make deploy-app
   ```

6. **Run tests**:
   ```bash
   make test
   ```

## 🔧 Development

### Local Development

```bash
# Start local development environment
make dev

# Run linting and formatting
make lint

# Run tests
make test

# Package Lambda functions
make package
```

### Environment Variables

Key environment variables (managed via AWS Secrets Manager):

- `LEX_BOT_ID`: Amazon Lex bot identifier
- `DYNAMODB_TABLE`: DynamoDB table name
- `S3_BUCKET`: S3 bucket for audio storage
- `COGNITO_USER_POOL_ID`: Cognito user pool ID

## 📊 Monitoring

- **CloudWatch Dashboards**: Real-time metrics and KPIs
- **CloudWatch Alarms**: Automated alerting for errors and performance
- **X-Ray Tracing**: End-to-end request tracing
- **Custom Metrics**: Business-specific monitoring

## 🔒 Security

- **IAM Least Privilege**: Fine-grained permissions
- **Secrets Manager**: Secure credential storage
- **KMS Encryption**: Data encryption at rest and in transit
- **VPC Integration**: Network isolation
- **WAF Protection**: Web application firewall

## 💰 Cost Optimization

- **Reserved Concurrency**: Predictable Lambda costs
- **DynamoDB On-Demand**: Pay-per-request pricing
- **S3 Lifecycle Policies**: Automated data archival
- **CloudWatch Log Retention**: Optimized log storage

## 📚 Documentation

- [Architecture Guide](docs/architecture.md)
- [Operations Runbook](docs/runbook.md)
- [Cost Optimization](docs/cost_optimization.md)
- [Security Guidelines](docs/security.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For issues and questions:
- Check the [troubleshooting guide](docs/troubleshooting.md)
- Review CloudWatch logs
- Contact the development team

---

**Built with ❤️ for production-scale voice AI applications**
