const express = require('express');
const {
  getPlaylists,
  createPlaylist,
  getPlaylistById,
  updatePlaylist,
  deletePlaylist
} = require('../controllers/playlistController');
const verifyToken = require('../middlewares/verifyToken');

const router = express.Router();

// All playlist routes require authentication
router.use(verifyToken);

// Playlist routes
router.get('/', getPlaylists);
router.post('/', createPlaylist);
router.get('/:id', getPlaylistById);
router.put('/:id', updatePlaylist);
router.delete('/:id', deletePlaylist);

module.exports = router;
