import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Upload, ArrowLeft, Music } from 'lucide-react';
import api from '../api/axios';

const UploadSong = () => {
  const [formData, setFormData] = useState({
    title: '',
    artist: '',
    album: '',
    genre: ''
  });
  const [file, setFile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const navigate = useNavigate();

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile) {
      if (selectedFile.type.startsWith('audio/')) {
        setFile(selectedFile);
        setError('');
      } else {
        setError('Please select a valid audio file');
        setFile(null);
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    if (!file) {
      setError('Please select an audio file');
      setLoading(false);
      return;
    }

    const uploadData = new FormData();
    uploadData.append('title', formData.title);
    uploadData.append('artist', formData.artist);
    uploadData.append('album', formData.album);
    uploadData.append('genre', formData.genre);
    uploadData.append('audio', file);

    try {
      await api.post('/songs/upload', uploadData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      setSuccess('Song uploaded successfully!');
      setFormData({ title: '', artist: '', album: '', genre: '' });
      setFile(null);
      setTimeout(() => {
        navigate('/dashboard');
      }, 2000);
    } catch (error) {
      setError(error.response?.data?.error || 'Upload failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-spotify-black text-white">
      {/* Header */}
      <header className="bg-spotify-gray p-4 flex items-center space-x-4">
        <Link to="/dashboard" className="text-gray-400 hover:text-white">
          <ArrowLeft className="h-6 w-6" />
        </Link>
        <div className="flex items-center space-x-4">
          <Music className="h-8 w-8 text-spotify-green" />
          <h1 className="text-2xl font-bold">Upload Song</h1>
        </div>
      </header>

      {/* Main Content */}
      <main className="p-6 max-w-2xl mx-auto">
        <div className="mb-8">
          <h2 className="text-3xl font-bold mb-2">Share Your Music</h2>
          <p className="text-gray-400">Upload your song and share it with the world</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <div className="bg-red-500 text-white p-3 rounded-md text-sm">
              {error}
            </div>
          )}

          {success && (
            <div className="bg-green-500 text-white p-3 rounded-md text-sm">
              {success}
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-300 mb-2">
                Song Title *
              </label>
              <input
                id="title"
                name="title"
                type="text"
                required
                className="input-field"
                placeholder="Enter song title"
                value={formData.title}
                onChange={handleChange}
              />
            </div>

            <div>
              <label htmlFor="artist" className="block text-sm font-medium text-gray-300 mb-2">
                Artist *
              </label>
              <input
                id="artist"
                name="artist"
                type="text"
                required
                className="input-field"
                placeholder="Enter artist name"
                value={formData.artist}
                onChange={handleChange}
              />
            </div>

            <div>
              <label htmlFor="album" className="block text-sm font-medium text-gray-300 mb-2">
                Album
              </label>
              <input
                id="album"
                name="album"
                type="text"
                className="input-field"
                placeholder="Enter album name"
                value={formData.album}
                onChange={handleChange}
              />
            </div>

            <div>
              <label htmlFor="genre" className="block text-sm font-medium text-gray-300 mb-2">
                Genre
              </label>
              <input
                id="genre"
                name="genre"
                type="text"
                className="input-field"
                placeholder="Enter genre"
                value={formData.genre}
                onChange={handleChange}
              />
            </div>
          </div>

          <div>
            <label htmlFor="audio" className="block text-sm font-medium text-gray-300 mb-2">
              Audio File *
            </label>
            <div className="border-2 border-dashed border-gray-600 rounded-lg p-6 text-center">
              <input
                id="audio"
                name="audio"
                type="file"
                accept="audio/*"
                required
                className="hidden"
                onChange={handleFileChange}
              />
              <label
                htmlFor="audio"
                className="cursor-pointer flex flex-col items-center space-y-2"
              >
                <Upload className="h-12 w-12 text-gray-400" />
                <span className="text-gray-400">
                  {file ? file.name : 'Click to select audio file'}
                </span>
                <span className="text-sm text-gray-500">
                  Supported formats: MP3, WAV, FLAC, AAC
                </span>
              </label>
            </div>
          </div>

          <div className="flex space-x-4">
            <button
              type="submit"
              disabled={loading}
              className="flex-1 btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              <Upload className="h-4 w-4" />
              <span>{loading ? 'Uploading...' : 'Upload Song'}</span>
            </button>
            <Link
              to="/dashboard"
              className="flex-1 btn-secondary flex items-center justify-center space-x-2"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Cancel</span>
            </Link>
          </div>
        </form>
      </main>
    </div>
  );
};

export default UploadSong;
