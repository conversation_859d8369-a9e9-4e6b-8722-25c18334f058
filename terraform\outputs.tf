# Terraform Outputs

output "instance_id" {
  description = "ID of the EC2 instance"
  value       = aws_instance.spotify_server.id
}

output "instance_public_ip" {
  description = "Public IP address of the EC2 instance"
  value       = aws_eip.spotify_eip.public_ip
}

output "instance_public_dns" {
  description = "Public DNS name of the EC2 instance"
  value       = aws_instance.spotify_server.public_dns
}

output "instance_private_ip" {
  description = "Private IP address of the EC2 instance"
  value       = aws_instance.spotify_server.private_ip
}

output "security_group_id" {
  description = "ID of the security group"
  value       = aws_security_group.spotify_sg.id
}

output "s3_bucket_name" {
  description = "Name of the S3 bucket"
  value       = aws_s3_bucket.spotify_bucket.bucket
}

output "s3_bucket_arn" {
  description = "ARN of the S3 bucket"
  value       = aws_s3_bucket.spotify_bucket.arn
}

output "s3_bucket_domain_name" {
  description = "Domain name of the S3 bucket"
  value       = aws_s3_bucket.spotify_bucket.bucket_domain_name
}

output "iam_role_arn" {
  description = "ARN of the IAM role"
  value       = aws_iam_role.spotify_ec2_role.arn
}

output "application_url" {
  description = "URL to access the Spotify clone application"
  value       = "http://${aws_eip.spotify_eip.public_ip}:3000"
}

output "ssh_connection" {
  description = "SSH connection command"
  value       = "ssh -i ${var.key_pair_name}.pem ubuntu@${aws_eip.spotify_eip.public_ip}"
}
