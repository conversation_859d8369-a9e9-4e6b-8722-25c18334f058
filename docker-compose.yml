version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: spotify-mysql
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: spotify_clone
      MYSQL_USER: spotify_user
      MYSQL_PASSWORD: spotify_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    networks:
      - spotify-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: spotify-backend
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_USER=spotify_user
      - DB_PASSWORD=spotify_password
      - DB_NAME=spotify_clone
      - JWT_SECRET=your-super-secret-jwt-key
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION}
      - S3_BUCKET_NAME=${S3_BUCKET_NAME}
    ports:
      - "5000:5000"
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - spotify-network
    volumes:
      - ./backend:/app
      - /app/node_modules

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: spotify-frontend
    ports:
      - "3000:80"
    depends_on:
      - backend
    networks:
      - spotify-network
    environment:
      - VITE_API_URL=http://localhost:5000/api

  # Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: spotify-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - spotify-network

  # Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: spotify-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana-provisioning:/etc/grafana/provisioning
    depends_on:
      - prometheus
    networks:
      - spotify-network

  # Node Exporter
  node-exporter:
    image: prom/node-exporter:latest
    container_name: spotify-node-exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - spotify-network

volumes:
  mysql_data:
  prometheus_data:
  grafana_data:

networks:
  spotify-network:
    driver: bridge
