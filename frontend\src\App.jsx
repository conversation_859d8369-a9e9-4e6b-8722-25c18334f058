import React from 'react'
import { <PERSON>rowserRouter as Router, Routes, Route } from 'react-router-dom'
import Login from './auth/Login'
import Register from './auth/Register'
import Dashboard from './pages/Dashboard'
import UploadSong from './pages/UploadSong'

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-spotify-black">
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/upload" element={<UploadSong />} />
          <Route path="/" element={<Login />} />
        </Routes>
      </div>
    </Router>
  )
}

export default App
