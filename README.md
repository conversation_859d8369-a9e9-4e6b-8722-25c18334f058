# Spotify Clone

A full-stack Spotify clone application with modern DevOps practices.

## Architecture

- **Backend**: Node.js + Express + JWT + MySQL + S3
- **Frontend**: React + Vite + Tailwind CSS
- **Infrastructure**: AWS EC2 + S3 + Terraform
- **Monitoring**: Prometheus + Grafana
- **CI/CD**: Jenkins + Ansible
- **Containerization**: Docker + Docker Compose

## Quick Start

### Prerequisites
- Docker and Docker Compose
- Node.js 18+
- AWS Account (for S3 storage)

### Local Development

1. Clone the repository:
```bash
git clone <your-repo-url>
cd spotify-clone
```

2. Set up environment variables:
```bash
cp backend/.env.example backend/.env
# Edit backend/.env with your configuration
```

3. Start the application:
```bash
docker-compose up -d
```

4. Access the application:
- Frontend: http://localhost:3000
- Backend API: http://localhost:5000
- Grafana: http://localhost:3001 (admin/admin)
- Prometheus: http://localhost:9090

## Project Structure

```
spotify-clone/
├── backend/                  ← Node.js + Express + JWT + S3 + MySQL
├── frontend/                 ← React + Tailwind CSS + Axios
├── monitoring/              ← Prometheus + Grafana manifests
├── terraform/               ← EC2 Spot instance + IAM + S3 bucket
├── ansible/                 ← Bootstrap EC2 (Docker + env from SSM)
├── jenkins/                 ← Local Jenkins pipeline
└── docker-compose.yml       ← Backend, Frontend, MySQL, Prometheus, Grafana
```

## Deployment

### AWS Infrastructure

1. Deploy infrastructure with Terraform:
```bash
cd terraform
terraform init
terraform plan
terraform apply
```

2. Configure and run Ansible:
```bash
cd ansible
# Update inventory.ini with your EC2 IP
ansible-playbook -i inventory.ini playbook.yml
```

### CI/CD Pipeline

The Jenkins pipeline automatically:
- Builds Docker images
- Runs tests
- Performs security scans
- Deploys to staging/production

## Features

- User authentication (register/login)
- Music upload and streaming
- Playlist management
- Real-time monitoring
- Scalable infrastructure
- Automated deployments

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
