# IAM Configuration

# IAM Role for EC2 instance
resource "aws_iam_role" "spotify_ec2_role" {
  name = "${var.project_name}-${var.environment}-ec2-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })

  tags = {
    Name        = "${var.project_name}-${var.environment}-ec2-role"
    Project     = var.project_name
    Environment = var.environment
  }
}

# IAM Policy for S3 access
resource "aws_iam_policy" "spotify_s3_policy" {
  name        = "${var.project_name}-${var.environment}-s3-policy"
  description = "Policy for Spotify clone S3 access"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject",
          "s3:ListBucket"
        ]
        Resource = [
          aws_s3_bucket.spotify_bucket.arn,
          "${aws_s3_bucket.spotify_bucket.arn}/*"
        ]
      }
    ]
  })

  tags = {
    Name        = "${var.project_name}-${var.environment}-s3-policy"
    Project     = var.project_name
    Environment = var.environment
  }
}

# IAM Policy for CloudWatch logs
resource "aws_iam_policy" "spotify_cloudwatch_policy" {
  name        = "${var.project_name}-${var.environment}-cloudwatch-policy"
  description = "Policy for CloudWatch logs access"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "logs:DescribeLogStreams"
        ]
        Resource = "arn:aws:logs:*:*:*"
      }
    ]
  })

  tags = {
    Name        = "${var.project_name}-${var.environment}-cloudwatch-policy"
    Project     = var.project_name
    Environment = var.environment
  }
}

# Attach S3 policy to role
resource "aws_iam_role_policy_attachment" "spotify_s3_policy_attachment" {
  role       = aws_iam_role.spotify_ec2_role.name
  policy_arn = aws_iam_policy.spotify_s3_policy.arn
}

# Attach CloudWatch policy to role
resource "aws_iam_role_policy_attachment" "spotify_cloudwatch_policy_attachment" {
  role       = aws_iam_role.spotify_ec2_role.name
  policy_arn = aws_iam_policy.spotify_cloudwatch_policy.arn
}

# IAM Instance Profile
resource "aws_iam_instance_profile" "spotify_profile" {
  name = "${var.project_name}-${var.environment}-instance-profile"
  role = aws_iam_role.spotify_ec2_role.name

  tags = {
    Name        = "${var.project_name}-${var.environment}-instance-profile"
    Project     = var.project_name
    Environment = var.environment
  }
}
