# Core AWS SDK
boto3==1.28.25
botocore==1.31.25

# Lambda runtime dependencies
aws-lambda-powertools==2.20.0
aws-xray-sdk==2.12.1

# Web framework for local development
fastapi==0.101.0
uvicorn==0.23.1

# Data processing
pydantic==2.1.1
pydantic-settings==2.0.2

# HTTP client
requests==2.31.0
httpx==0.24.1

# JSON processing
orjson==3.9.2

# Date/time handling
python-dateutil==2.8.2
pytz==2023.3

# Cryptography and security
cryptography==41.0.3
PyJWT==2.8.0

# Testing
pytest==7.4.0
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.11.1
moto==4.1.15

# Code quality
black==23.7.0
flake8==6.0.0
isort==5.12.0
bandit==1.7.5
mypy==1.4.1

# Development tools
pre-commit==3.3.3
python-dotenv==1.0.0

# Logging
structlog==23.1.0

# Audio processing (for voice features)
librosa==0.10.1
soundfile==0.12.1
pydub==0.25.1

# Natural language processing
nltk==3.8.1
spacy==3.6.1

# Machine learning (optional for advanced features)
scikit-learn==1.3.0
numpy==1.24.3

# Database utilities
pymongo==4.4.1  # If using MongoDB
redis==4.6.0    # For caching

# Monitoring and observability
prometheus-client==0.17.1

# Configuration management
python-decouple==3.8

# Utilities
click==8.1.6
rich==13.4.2
tenacity==8.2.2

# Development dependencies
ipython==8.14.0
jupyter==1.0.0

# Documentation
sphinx==7.1.1
sphinx-rtd-theme==1.3.0

# Type hints
types-requests==********
types-python-dateutil==*********
