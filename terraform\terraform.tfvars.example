# Example Terraform variables file
# Copy this file to terraform.tfvars and update the values

# AWS Configuration
aws_region = "us-east-1"

# Project Configuration
project_name = "spotify-clone"
environment  = "dev"

# EC2 Configuration
instance_type   = "t3.medium"
key_pair_name   = "spotify-clone-key"  # Make sure this key pair exists in your AWS account

# Security Configuration
allowed_cidr_blocks = [
  "0.0.0.0/0"  # Allow access from anywhere (change this for production)
  # "YOUR_IP/32"  # Uncomment and replace with your IP for better security
]

# S3 Configuration
s3_bucket_name = "spotify-clone-files"  # Will have random suffix added automatically
