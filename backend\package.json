{"name": "spotify-clone-backend", "version": "1.0.0", "description": "Backend API for Spotify Clone application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["spotify", "music", "api", "nodejs", "express"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "mysql2": "^3.6.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "aws-sdk": "^2.1467.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0"}}