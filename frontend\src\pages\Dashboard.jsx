import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Play, Pause, Upload, LogOut, Music } from 'lucide-react';
import api from '../api/axios';

const spotifyGreen = '#1DB954';

const Dashboard = () => {
  const [songs, setSongs] = useState([]);
  const [currentSong, setCurrentSong] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');
    if (!token) {
      navigate('/login');
      return;
    }
    if (userData) {
      setUser(JSON.parse(userData));
    }
    fetchSongs();
    // eslint-disable-next-line
  }, [navigate]);

  const fetchSongs = async () => {
    try {
      const response = await api.get('/songs');
      setSongs(response.data);
    } catch (error) {
      console.error('Error fetching songs:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    navigate('/login');
  };

  const togglePlay = (song) => {
    if (currentSong?.id === song.id) {
      setIsPlaying(!isPlaying);
    } else {
      setCurrentSong(song);
      setIsPlaying(true);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-white text-xl">Loading...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex bg-black text-white">
      {/* Sidebar */}
      <aside className="w-64 bg-[#181818] flex flex-col p-6 min-h-screen">
        <div className="flex items-center mb-10">
          <img src="https://storage.googleapis.com/pr-newsroom-wp/1/2018/11/Spotify_Logo_CMYK_Green.png" alt="Spotify Logo" className="w-32" />
        </div>
        <nav className="flex flex-col gap-4">
          <Link to="/dashboard" className="text-lg font-semibold hover:text-green-400 transition">Home</Link>
          <Link to="/upload" className="text-lg font-semibold hover:text-green-400 transition">Upload Song</Link>
        </nav>
        <div className="mt-auto">
          <button
            onClick={handleLogout}
            className="w-full py-2 rounded-md font-bold text-white mt-8"
            style={{ background: spotifyGreen }}
          >
            <LogOut className="inline-block mr-2" /> Logout
          </button>
        </div>
      </aside>
      {/* Main Content */}
      <main className="flex-1 p-10">
        <header className="flex justify-between items-center mb-10">
          <div>
            <h1 className="text-4xl font-extrabold mb-1">Welcome, {user?.username}</h1>
            <p className="text-gray-400">Enjoy your favorite music, Spotify style!</p>
          </div>
          <Link
            to="/upload"
            className="py-2 px-6 rounded-full font-bold text-white shadow-lg"
            style={{ background: spotifyGreen }}
          >
            <Upload className="inline-block mr-2" /> Upload Song
          </Link>
        </header>
        <section>
          <h2 className="text-2xl font-bold mb-6">Your Music</h2>
          {songs.length === 0 ? (
            <div className="text-center py-12">
              <Music className="h-16 w-16 text-gray-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">No songs yet</h3>
              <p className="text-gray-400 mb-6">Upload your first song to get started</p>
              <Link to="/upload" className="btn-primary">
                Upload Song
              </Link>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {songs.map((song) => (
                <div
                  key={song.id}
                  className="bg-spotify-gray p-4 rounded-lg hover:bg-gray-700 transition-colors"
                >
                  <div className="aspect-square bg-gray-600 rounded-lg mb-4 flex items-center justify-center">
                    <Music className="h-12 w-12 text-gray-400" />
                  </div>
                  <h3 className="font-semibold mb-1 truncate">{song.title}</h3>
                  <p className="text-gray-400 text-sm mb-3 truncate">{song.artist}</p>
                  <button
                    onClick={() => togglePlay(song)}
                    className="w-full btn-primary flex items-center justify-center space-x-2"
                  >
                    {currentSong?.id === song.id && isPlaying ? (
                      <Pause className="h-4 w-4" />
                    ) : (
                      <Play className="h-4 w-4" />
                    )}
                    <span>
                      {currentSong?.id === song.id && isPlaying ? 'Pause' : 'Play'}
                    </span>
                  </button>
                </div>
              ))}
            </div>
          )}
        </section>
      </main>

      {/* Now Playing Bar */}
      {currentSong && (
        <div className="fixed bottom-0 left-0 right-0 bg-spotify-gray p-4 border-t border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gray-600 rounded flex items-center justify-center">
                <Music className="h-6 w-6 text-gray-400" />
              </div>
              <div>
                <h4 className="font-semibold">{currentSong.title}</h4>
                <p className="text-gray-400 text-sm">{currentSong.artist}</p>
              </div>
            </div>
            <button
              onClick={() => togglePlay(currentSong)}
              className="btn-primary"
            >
              {isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;
