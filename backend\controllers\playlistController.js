const { pool } = require('../models/db');

const getPlaylists = async (req, res) => {
  try {
    const [rows] = await pool.execute(
      'SELECT * FROM playlists WHERE user_id = ? ORDER BY created_at DESC',
      [req.userId]
    );
    
    res.json(rows);
  } catch (error) {
    console.error('Get playlists error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const createPlaylist = async (req, res) => {
  try {
    const { name, description } = req.body;
    
    const [result] = await pool.execute(
      'INSERT INTO playlists (name, description, user_id) VALUES (?, ?, ?)',
      [name, description, req.userId]
    );
    
    res.status(201).json({
      id: result.insertId,
      name,
      description,
      user_id: req.userId,
      message: 'Playlist created successfully'
    });
  } catch (error) {
    console.error('Create playlist error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const getPlaylistById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const [rows] = await pool.execute(
      'SELECT * FROM playlists WHERE id = ? AND user_id = ?',
      [id, req.userId]
    );
    
    if (rows.length === 0) {
      return res.status(404).json({ error: 'Playlist not found' });
    }
    
    res.json(rows[0]);
  } catch (error) {
    console.error('Get playlist error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const updatePlaylist = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description } = req.body;
    
    const [result] = await pool.execute(
      'UPDATE playlists SET name = ?, description = ? WHERE id = ? AND user_id = ?',
      [name, description, id, req.userId]
    );
    
    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Playlist not found' });
    }
    
    res.json({ message: 'Playlist updated successfully' });
  } catch (error) {
    console.error('Update playlist error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const deletePlaylist = async (req, res) => {
  try {
    const { id } = req.params;
    
    const [result] = await pool.execute(
      'DELETE FROM playlists WHERE id = ? AND user_id = ?',
      [id, req.userId]
    );
    
    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'Playlist not found' });
    }
    
    res.json({ message: 'Playlist deleted successfully' });
  } catch (error) {
    console.error('Delete playlist error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  getPlaylists,
  createPlaylist,
  getPlaylistById,
  updatePlaylist,
  deletePlaylist
};
