# S3 Bucket Configuration

# S3 Bucket for storing music files and assets
resource "aws_s3_bucket" "spotify_bucket" {
  bucket = "${var.s3_bucket_name}-${var.environment}-${random_string.bucket_suffix.result}"

  tags = {
    Name        = "${var.project_name}-${var.environment}-bucket"
    Project     = var.project_name
    Environment = var.environment
  }
}

# Random string for unique bucket naming
resource "random_string" "bucket_suffix" {
  length  = 8
  special = false
  upper   = false
}

# S3 Bucket versioning
resource "aws_s3_bucket_versioning" "spotify_bucket_versioning" {
  bucket = aws_s3_bucket.spotify_bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}

# S3 Bucket encryption
resource "aws_s3_bucket_server_side_encryption_configuration" "spotify_bucket_encryption" {
  bucket = aws_s3_bucket.spotify_bucket.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

# S3 Bucket public access block
resource "aws_s3_bucket_public_access_block" "spotify_bucket_pab" {
  bucket = aws_s3_bucket.spotify_bucket.id

  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false
}

# S3 Bucket policy for public read access to music files
resource "aws_s3_bucket_policy" "spotify_bucket_policy" {
  bucket = aws_s3_bucket.spotify_bucket.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "PublicReadGetObject"
        Effect    = "Allow"
        Principal = "*"
        Action    = "s3:GetObject"
        Resource  = "${aws_s3_bucket.spotify_bucket.arn}/public/*"
      }
    ]
  })

  depends_on = [aws_s3_bucket_public_access_block.spotify_bucket_pab]
}

# S3 Bucket CORS configuration
resource "aws_s3_bucket_cors_configuration" "spotify_bucket_cors" {
  bucket = aws_s3_bucket.spotify_bucket.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "PUT", "POST", "DELETE", "HEAD"]
    allowed_origins = ["*"]
    expose_headers  = ["ETag"]
    max_age_seconds = 3000
  }
}
