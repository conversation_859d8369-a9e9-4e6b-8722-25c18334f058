#!/bin/bash

set -e

ENVIRONMENT=$1

if [ -z "$ENVIRONMENT" ]; then
    echo "Usage: $0 <environment>"
    echo "Available environments: staging, production"
    exit 1
fi

echo "Deploying to $ENVIRONMENT..."

case $ENVIRONMENT in
    "staging")
        echo "Deploying to staging environment..."
        ansible-playbook -i ansible/inventory-staging.ini ansible/playbook.yml
        ;;
    "production")
        echo "Deploying to production environment..."
        ansible-playbook -i ansible/inventory.ini ansible/playbook.yml
        ;;
    *)
        echo "Unknown environment: $ENVIRONMENT"
        exit 1
        ;;
esac

echo "Deployment to $ENVIRONMENT completed successfully!"
