const { pool } = require('./db');

class User {
  static async create(userData) {
    const { username, email, password } = userData;
    const [result] = await pool.execute(
      'INSERT INTO users (username, email, password) VALUES (?, ?, ?)',
      [username, email, password]
    );
    
    return {
      id: result.insertId,
      username,
      email
    };
  }

  static async findOne(options) {
    const { where } = options;
    let query = 'SELECT * FROM users WHERE ';
    let params = [];
    
    if (where.email) {
      query += 'email = ?';
      params.push(where.email);
    } else if (where.id) {
      query += 'id = ?';
      params.push(where.id);
    }
    
    const [rows] = await pool.execute(query, params);
    return rows.length > 0 ? rows[0] : null;
  }

  static async findByPk(id, options = {}) {
    const { attributes } = options;
    let query = 'SELECT ';
    
    if (attributes && attributes.length > 0) {
      query += attributes.join(', ');
    } else {
      query += '*';
    }
    
    query += ' FROM users WHERE id = ?';
    
    const [rows] = await pool.execute(query, [id]);
    return rows.length > 0 ? rows[0] : null;
  }

  static async findAll(options = {}) {
    const { attributes, where } = options;
    let query = 'SELECT ';
    
    if (attributes && attributes.length > 0) {
      query += attributes.join(', ');
    } else {
      query += '*';
    }
    
    query += ' FROM users';
    let params = [];
    
    if (where) {
      query += ' WHERE ';
      const conditions = [];
      
      Object.keys(where).forEach(key => {
        conditions.push(`${key} = ?`);
        params.push(where[key]);
      });
      
      query += conditions.join(' AND ');
    }
    
    const [rows] = await pool.execute(query, params);
    return rows;
  }

  static async update(id, updateData) {
    const fields = Object.keys(updateData);
    const values = Object.values(updateData);
    
    const setClause = fields.map(field => `${field} = ?`).join(', ');
    const query = `UPDATE users SET ${setClause} WHERE id = ?`;
    
    values.push(id);
    
    const [result] = await pool.execute(query, values);
    return result.affectedRows > 0;
  }

  static async delete(id) {
    const [result] = await pool.execute('DELETE FROM users WHERE id = ?', [id]);
    return result.affectedRows > 0;
  }
}

module.exports = User;
