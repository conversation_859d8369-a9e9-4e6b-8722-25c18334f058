version: 1
applications:
  - frontend:
      phases:
        preBuild:
          commands:
            - echo "Installing dependencies..."
            - npm ci
            - echo "Setting up environment variables..."
            - |
              cat > .env.production << EOF
              REACT_APP_API_GATEWAY_URL=$API_GATEWAY_URL
              REACT_APP_COGNITO_USER_POOL_ID=$COGNITO_USER_POOL_ID
              REACT_APP_COGNITO_CLIENT_ID=$COGNITO_CLIENT_ID
              REACT_APP_COGNITO_IDENTITY_POOL_ID=$COGNITO_IDENTITY_POOL_ID
              REACT_APP_COGNITO_DOMAIN=$COGNITO_DOMAIN
              REACT_APP_S3_BUCKET=$S3_BUCKET
              REACT_APP_AWS_REGION=$AWS_REGION
              REACT_APP_ENVIRONMENT=$ENVIRONMENT
              REACT_APP_VERSION=$AWS_COMMIT_ID
              EOF
        build:
          commands:
            - echo "Building React application..."
            - npm run build
            - echo "Running tests..."
            - npm run test -- --coverage --watchAll=false
            - echo "Running linting..."
            - npm run lint
            - echo "Type checking..."
            - npm run type-check
        postBuild:
          commands:
            - echo "Build completed successfully"
            - echo "Generating build report..."
            - |
              cat > build-report.json << EOF
              {
                "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
                "commit": "$AWS_COMMIT_ID",
                "branch": "$AWS_BRANCH",
                "build_id": "$AWS_BUILD_ID",
                "environment": "$ENVIRONMENT",
                "status": "success"
              }
              EOF
      artifacts:
        baseDirectory: build
        files:
          - '**/*'
      cache:
        paths:
          - node_modules/**/*
    appRoot: frontend
    customHeaders:
      - pattern: '**/*'
        headers:
          - key: 'Strict-Transport-Security'
            value: 'max-age=31536000; includeSubDomains'
          - key: 'X-Content-Type-Options'
            value: 'nosniff'
          - key: 'X-Frame-Options'
            value: 'DENY'
          - key: 'X-XSS-Protection'
            value: '1; mode=block'
          - key: 'Referrer-Policy'
            value: 'strict-origin-when-cross-origin'
          - key: 'Content-Security-Policy'
            value: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://*.amazonaws.com https://*.amplifyapp.com"
      - pattern: '/static/js/*.js'
        headers:
          - key: 'Cache-Control'
            value: 'public, max-age=31536000, immutable'
      - pattern: '/static/css/*.css'
        headers:
          - key: 'Cache-Control'
            value: 'public, max-age=31536000, immutable'
      - pattern: '/static/media/*'
        headers:
          - key: 'Cache-Control'
            value: 'public, max-age=31536000, immutable'
    environmentVariables:
      - name: AMPLIFY_DIFF_DEPLOY
        value: false
      - name: AMPLIFY_MONOREPO_APP_ROOT
        value: frontend
      - name: _LIVE_UPDATES
        value: '[{"name":"Amplify CLI","pkg":"@aws-amplify/cli","type":"npm","version":"latest"}]'
